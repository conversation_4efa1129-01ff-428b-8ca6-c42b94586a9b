package com.agriculture.cropprediction.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AdvancedCropPredictionService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdvancedCropPredictionService.class);
    
    private List<CropData> cropDatabase;
    private Map<String, CropProfile> cropProfiles;
    
    public static class CropData {
        public double N, P, K, temperature, humidity, ph, rainfall;
        public String label;
        
        public CropData(double N, double P, double K, double temperature, double humidity, double ph, double rainfall, String label) {
            this.N = N;
            this.P = P;
            this.K = K;
            this.temperature = temperature;
            this.humidity = humidity;
            this.ph = ph;
            this.rainfall = rainfall;
            this.label = label;
        }
    }
    
    public static class CropProfile {
        public String cropName;
        public Map<String, Double> minValues = new HashMap<>();
        public Map<String, Double> maxValues = new HashMap<>();
        public Map<String, Double> meanValues = new HashMap<>();
        public Map<String, Double> optimalMinValues = new HashMap<>();
        public Map<String, Double> optimalMaxValues = new HashMap<>();
        
        public CropProfile(String cropName) {
            this.cropName = cropName;
        }
    }
    
    public static class PredictionResult {
        public String crop;
        public double confidence;
        public double confidencePercentage;
        
        public PredictionResult(String crop, double confidence) {
            this.crop = crop;
            this.confidence = confidence;
            this.confidencePercentage = confidence * 100;
        }
    }
    
    public static class RecommendationResult {
        public String parameter;
        public double currentValue;
        public double optimalMin;
        public double optimalMax;
        public double recommendedValue;
        public String status;
        public String action;
        public String priority;
        public double gap;
        public String recommendation;
        
        public RecommendationResult(String parameter, double currentValue, double optimalMin, double optimalMax, double recommendedValue) {
            this.parameter = parameter;
            this.currentValue = currentValue;
            this.optimalMin = optimalMin;
            this.optimalMax = optimalMax;
            this.recommendedValue = recommendedValue;
            this.gap = 0.0;
            this.status = "optimal";
            this.action = "maintain";
            this.priority = "LOW";
        }
    }
    
    public AdvancedCropPredictionService() {
        try {
            loadCropDatabase();
            createCropProfiles();
            logger.info("✅ AdvancedCropPredictionService initialized successfully");
        } catch (Exception e) {
            logger.error("❌ Failed to initialize AdvancedCropPredictionService: {}", e.getMessage());
            // Initialize with empty data to prevent startup failure
            cropDatabase = new ArrayList<>();
            cropProfiles = new HashMap<>();
        }
    }
    
    private void loadCropDatabase() {
        logger.info("Loading crop recommendation database...");
        cropDatabase = new ArrayList<>();
        
        try {
            ClassPathResource resource = new ClassPathResource("ml-data/Crop_recommendation.csv");
            BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
            
            String line = reader.readLine(); // Skip header
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length >= 8) {
                    try {
                        double N = Double.parseDouble(parts[0]);
                        double P = Double.parseDouble(parts[1]);
                        double K = Double.parseDouble(parts[2]);
                        double temperature = Double.parseDouble(parts[3]);
                        double humidity = Double.parseDouble(parts[4]);
                        double ph = Double.parseDouble(parts[5]);
                        double rainfall = Double.parseDouble(parts[6]);
                        String label = parts[7].trim();
                        
                        cropDatabase.add(new CropData(N, P, K, temperature, humidity, ph, rainfall, label));
                    } catch (NumberFormatException e) {
                        logger.warn("Error parsing line: {}", line);
                    }
                }
            }
            reader.close();
            
            logger.info("✅ Loaded {} crop records from database", cropDatabase.size());
            
        } catch (Exception e) {
            logger.error("❌ Error loading crop database: {}", e.getMessage());
            // Create fallback data if file not found
            createFallbackData();
        }
    }
    
    private void createFallbackData() {
        logger.info("Creating fallback crop data...");
        cropDatabase = new ArrayList<>();
        
        // Add some basic crop data as fallback
        // Rice data
        for (int i = 0; i < 50; i++) {
            cropDatabase.add(new CropData(
                80 + Math.random() * 40,  // N: 80-120
                40 + Math.random() * 20,  // P: 40-60
                40 + Math.random() * 20,  // K: 40-60
                20 + Math.random() * 15,  // temp: 20-35
                80 + Math.random() * 15,  // humidity: 80-95
                5.5 + Math.random() * 1.5, // ph: 5.5-7.0
                150 + Math.random() * 150, // rainfall: 150-300
                "rice"
            ));
        }
        
        // Wheat data
        for (int i = 0; i < 50; i++) {
            cropDatabase.add(new CropData(
                50 + Math.random() * 50,  // N: 50-100
                30 + Math.random() * 20,  // P: 30-50
                30 + Math.random() * 20,  // K: 30-50
                15 + Math.random() * 10,  // temp: 15-25
                50 + Math.random() * 20,  // humidity: 50-70
                6.0 + Math.random() * 1.5, // ph: 6.0-7.5
                50 + Math.random() * 100,  // rainfall: 50-150
                "wheat"
            ));
        }
        
        // Corn data
        for (int i = 0; i < 50; i++) {
            cropDatabase.add(new CropData(
                100 + Math.random() * 50, // N: 100-150
                40 + Math.random() * 40,  // P: 40-80
                60 + Math.random() * 60,  // K: 60-120
                18 + Math.random() * 12,  // temp: 18-30
                60 + Math.random() * 20,  // humidity: 60-80
                6.0 + Math.random() * 1.0, // ph: 6.0-7.0
                100 + Math.random() * 100, // rainfall: 100-200
                "corn"
            ));
        }
        
        // Cotton data
        for (int i = 0; i < 50; i++) {
            cropDatabase.add(new CropData(
                100 + Math.random() * 40, // N: 100-140
                20 + Math.random() * 20,  // P: 20-40
                150 + Math.random() * 50, // K: 150-200
                25 + Math.random() * 10,  // temp: 25-35
                50 + Math.random() * 20,  // humidity: 50-70
                5.5 + Math.random() * 2.5, // ph: 5.5-8.0
                50 + Math.random() * 50,   // rainfall: 50-100
                "cotton"
            ));
        }
        
        // Potato data
        for (int i = 0; i < 50; i++) {
            cropDatabase.add(new CropData(
                80 + Math.random() * 40,  // N: 80-120
                50 + Math.random() * 30,  // P: 50-80
                100 + Math.random() * 50, // K: 100-150
                15 + Math.random() * 10,  // temp: 15-25
                70 + Math.random() * 15,  // humidity: 70-85
                5.0 + Math.random() * 1.5, // ph: 5.0-6.5
                80 + Math.random() * 40,   // rainfall: 80-120
                "potato"
            ));
        }
        
        logger.info("✅ Created {} fallback crop records", cropDatabase.size());
    }
    
    private void createCropProfiles() {
        logger.info("Creating crop profiles...");
        cropProfiles = new HashMap<>();
        
        // Group data by crop
        Map<String, List<CropData>> cropGroups = cropDatabase.stream()
            .collect(Collectors.groupingBy(crop -> crop.label));
        
        for (Map.Entry<String, List<CropData>> entry : cropGroups.entrySet()) {
            String cropName = entry.getKey();
            List<CropData> cropData = entry.getValue();
            
            CropProfile profile = new CropProfile(cropName);
            
            // Calculate statistics for each parameter
            String[] parameters = {"N", "P", "K", "temperature", "humidity", "ph", "rainfall"};
            
            for (String param : parameters) {
                List<Double> values = cropData.stream()
                    .map(crop -> getParameterValue(crop, param))
                    .collect(Collectors.toList());
                
                values.sort(Double::compareTo);
                
                profile.minValues.put(param, values.get(0));
                profile.maxValues.put(param, values.get(values.size() - 1));
                profile.meanValues.put(param, values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0));
                
                // Calculate optimal range (25th to 75th percentile)
                int q1Index = (int) (values.size() * 0.25);
                int q3Index = (int) (values.size() * 0.75);
                profile.optimalMinValues.put(param, values.get(q1Index));
                profile.optimalMaxValues.put(param, values.get(q3Index));
            }
            
            cropProfiles.put(cropName, profile);
        }
        
        logger.info("✅ Created profiles for {} crops: {}", cropProfiles.size(), cropProfiles.keySet());
    }
    
    private double getParameterValue(CropData crop, String parameter) {
        switch (parameter) {
            case "N": return crop.N;
            case "P": return crop.P;
            case "K": return crop.K;
            case "temperature": return crop.temperature;
            case "humidity": return crop.humidity;
            case "ph": return crop.ph;
            case "rainfall": return crop.rainfall;
            default: return 0.0;
        }
    }
    
    public List<PredictionResult> predictCrops(Map<String, Double> inputData, int topN) {
        logger.info("Predicting crops for input data: {}", inputData);
        
        Map<String, Double> cropScores = new HashMap<>();
        
        // Calculate similarity scores for each crop
        for (String cropName : cropProfiles.keySet()) {
            double score = calculateCropSuitability(inputData, cropName);
            cropScores.put(cropName, score);
        }
        
        // Sort by score and return top N
        List<PredictionResult> results = cropScores.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .limit(topN)
            .map(entry -> new PredictionResult(entry.getKey(), entry.getValue()))
            .collect(Collectors.toList());
        
        logger.info("Top {} predictions: {}", topN, 
            results.stream().map(r -> r.crop + "(" + String.format("%.1f%%", r.confidencePercentage) + ")")
                .collect(Collectors.joining(", ")));
        
        return results;
    }
    
    private double calculateCropSuitability(Map<String, Double> inputData, String cropName) {
        CropProfile profile = cropProfiles.get(cropName);
        if (profile == null) return 0.0;
        
        String[] parameters = {"N", "P", "K", "temperature", "humidity", "ph", "rainfall"};
        List<Double> scores = new ArrayList<>();
        
        for (String param : parameters) {
            Double currentValue = inputData.get(param.toLowerCase());
            if (currentValue == null) {
                // Use default values if parameter is missing
                currentValue = getDefaultValue(param);
            }
            
            Double optimalMin = profile.optimalMinValues.get(param);
            Double optimalMax = profile.optimalMaxValues.get(param);
            
            if (optimalMin != null && optimalMax != null) {
                double score = calculateParameterScore(currentValue, optimalMin, optimalMax);
                scores.add(score);
            }
        }
        
        // Return average score
        return scores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    }
    
    private double getDefaultValue(String parameter) {
        switch (parameter) {
            case "N": return 50.0;
            case "P": return 50.0;
            case "K": return 50.0;
            case "temperature": return 25.0;
            case "humidity": return 70.0;
            case "ph": return 6.5;
            case "rainfall": return 100.0;
            default: return 50.0;
        }
    }
    
    private double calculateParameterScore(double currentValue, double optimalMin, double optimalMax) {
        if (currentValue >= optimalMin && currentValue <= optimalMax) {
            return 1.0; // Perfect score if within optimal range
        } else {
            // Calculate distance from optimal range
            double distance;
            double rangeSize = optimalMax - optimalMin;

            if (currentValue < optimalMin) {
                distance = optimalMin - currentValue;
            } else {
                distance = currentValue - optimalMax;
            }

            // Score decreases with distance, but never goes below 0
            double score = Math.max(0, 1.0 - (distance / (rangeSize * 2)));
            return score;
        }
    }

    public List<RecommendationResult> generateRecommendations(Map<String, Double> inputData, String targetCrop) {
        logger.info("Generating recommendations for crop: {}", targetCrop);

        CropProfile profile = cropProfiles.get(targetCrop);
        if (profile == null) {
            logger.warn("No profile found for crop: {}", targetCrop);
            return new ArrayList<>();
        }

        List<RecommendationResult> recommendations = new ArrayList<>();
        String[] parameters = {"N", "P", "K", "temperature", "humidity", "ph", "rainfall"};

        for (String param : parameters) {
            String paramKey = param.toLowerCase();
            Double currentValue = inputData.get(paramKey);
            if (currentValue == null) {
                currentValue = getDefaultValue(param);
            }

            Double optimalMin = profile.optimalMinValues.get(param);
            Double optimalMax = profile.optimalMaxValues.get(param);
            Double recommendedValue = profile.meanValues.get(param);

            if (optimalMin != null && optimalMax != null && recommendedValue != null) {
                RecommendationResult rec = new RecommendationResult(
                    param, currentValue, optimalMin, optimalMax, recommendedValue);

                // Determine status and recommendations
                if (currentValue < optimalMin) {
                    double gap = optimalMin - currentValue;
                    rec.status = "low";
                    rec.action = "increase";
                    rec.gap = gap;
                    rec.priority = getPriority(param, gap);
                    rec.recommendation = getIncreaseRecommendation(param, gap, targetCrop);
                } else if (currentValue > optimalMax) {
                    double gap = currentValue - optimalMax;
                    rec.status = "high";
                    rec.action = "decrease";
                    rec.gap = gap;
                    rec.priority = getPriority(param, gap);
                    rec.recommendation = getDecreaseRecommendation(param, gap, targetCrop);
                } else {
                    rec.priority = "LOW";  // Explicitly set priority for optimal values
                    rec.recommendation = param.toUpperCase() + " levels are optimal for " + targetCrop;
                }

                recommendations.add(rec);
            }
        }

        return recommendations;
    }

    private String getPriority(String parameter, double gap) {
        String[] criticalParams = {"N", "P", "K", "ph"};
        String[] importantParams = {"temperature", "humidity"};

        boolean isCritical = Arrays.asList(criticalParams).contains(parameter);
        boolean isImportant = Arrays.asList(importantParams).contains(parameter);

        if (isCritical) {
            if (gap > 20) return "CRITICAL";
            else if (gap > 10) return "HIGH";
            else return "MEDIUM";
        } else if (isImportant) {
            if (gap > 10) return "HIGH";
            else if (gap > 5) return "MEDIUM";
            else return "LOW";
        } else {
            if (gap > 50) return "HIGH";
            else if (gap > 20) return "MEDIUM";
            else return "LOW";
        }
    }

    private String getIncreaseRecommendation(String parameter, double gap, String crop) {
        switch (parameter) {
            case "N":
                return String.format("Apply nitrogen fertilizers: Urea (46-0-0) at 100-150 kg/ha, or Ammonium Sulfate (21-0-0) at 200-250 kg/ha. For %s, consider split applications during vegetative growth. Current deficit: %.1f units.", crop, gap);
            case "P":
                return String.format("Apply phosphorus fertilizers: Single Superphosphate (16%% P2O5) at 150-200 kg/ha, or DAP (18-46-0) at 100-150 kg/ha. Apply before planting for %s. Current deficit: %.1f units.", crop, gap);
            case "K":
                return String.format("Apply potassium fertilizers: Muriate of Potash (60%% K2O) at 100-150 kg/ha, or Sulfate of Potash for better quality in %s. Apply in split doses. Current deficit: %.1f units.", crop, gap);
            case "temperature":
                return String.format("Increase temperature: Use row covers, plastic mulch, or greenhouse structures. For %s, maintain optimal temperature through proper timing and protection. Current deficit: %.1f°C.", crop, gap);
            case "humidity":
                return String.format("Increase humidity: Install misting systems, increase irrigation frequency, or use mulching. %s benefits from consistent moisture levels. Current deficit: %.1f%%.", crop, gap);
            case "ph":
                return String.format("Increase soil pH: Apply agricultural lime at 1-2 tons/ha, or wood ash at 500-1000 kg/ha. Test soil pH regularly for %s cultivation. Current deficit: %.1f units.", crop, gap);
            case "rainfall":
                return String.format("Increase water supply: Install drip irrigation (20-30mm/week), or sprinkler systems. %s requires consistent water supply during critical growth stages. Current deficit: %.1f mm.", crop, gap);
            default:
                return String.format("Increase %s for optimal %s production. Current deficit: %.1f units.", parameter, crop, gap);
        }
    }

    private String getDecreaseRecommendation(String parameter, double gap, String crop) {
        switch (parameter) {
            case "N":
                return String.format("Reduce nitrogen: Skip nitrogen applications this season, plant legume cover crops, or apply carbon-rich materials. Excess nitrogen can delay %s maturity. Current excess: %.1f units.", crop, gap);
            case "P":
                return String.format("Reduce phosphorus: Avoid phosphorus fertilizers, apply iron or zinc to reduce P availability. High P can interfere with micronutrient uptake in %s. Current excess: %.1f units.", crop, gap);
            case "K":
                return String.format("Reduce potassium: Avoid potash applications, increase calcium and magnesium to balance nutrients. Excess K can affect %s quality. Current excess: %.1f units.", crop, gap);
            case "temperature":
                return String.format("Reduce temperature: Provide shade cloth (30-50%%), improve ventilation, or adjust planting dates. %s stress increases with high temperatures. Current excess: %.1f°C.", crop, gap);
            case "humidity":
                return String.format("Reduce humidity: Improve air circulation, reduce irrigation frequency, or install drainage. High humidity can cause disease issues in %s. Current excess: %.1f%%.", crop, gap);
            case "ph":
                return String.format("Decrease soil pH: Apply sulfur at 200-500 kg/ha, or organic matter like peat moss. %s prefers slightly acidic conditions for optimal nutrient uptake. Current excess: %.1f units.", crop, gap);
            case "rainfall":
                return String.format("Reduce water: Improve drainage, reduce irrigation, or plant on raised beds. Excess water can cause root rot and nutrient leaching in %s. Current excess: %.1f mm.", crop, gap);
            default:
                return String.format("Reduce %s for optimal %s production. Current excess: %.1f units.", parameter, crop, gap);
        }
    }

    public double calculateOverallSuitability(Map<String, Double> inputData, String cropName) {
        CropProfile profile = cropProfiles.get(cropName);
        if (profile == null) return 0.5;

        return calculateCropSuitability(inputData, cropName);
    }
}
