# Compiled class files
*.class

# Log files
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/

# Database
*.db
*.sqlite3
backend/data/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
uploads/
logs/
*.pkl
*.png
*.jpg
*.jpeg
*.gif

# Test and temporary files
*test*.py
*test*.bat
*test*.cmd
*test*.ps1
quick*.py
start*.py
debug*.bat
fix*.bat
minimal*.bat
simple*.bat
run*.cmd
start*.ps1

# Duplicate prevention
# Prevent ML model files in root
/crop_*.pkl
/crop_*.json

# Prevent duplicate directories in root
/Core Files/
/Data Files/
/Generated Outputes/
/__pycache__/

# Backend specific
backend/target/
backend/data/
backend/uploads/

# Frontend specific
frontend/build/
frontend/dist/

# ML Models (keep only in ml-models directory)
*.h5
*.joblib
*.model

# Reports and analysis
*.pdf
report_*.pdf

# Jupyter notebooks checkpoints
.ipynb_checkpoints/
