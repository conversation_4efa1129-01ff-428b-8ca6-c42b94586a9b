@echo off
setlocal

REM Clear all Python environment variables
set VIRTUAL_ENV=
set PYTHONPATH=
set CONDA_DEFAULT_ENV=
set CONDA_PREFIX=

echo ========================================
echo  Testing Login System
echo ========================================
echo.

cd /d "C:\Users\<USER>\Desktop\Model\agriculture-app\backend"

echo 🔧 Rebuilding backend...
call mvn clean package -DskipTests -q

echo.
echo 🚀 Starting backend in background...
start /B java -Dspring.profiles.active=test -jar target\crop-prediction-backend-0.0.1-SNAPSHOT.jar

echo ⏳ Waiting for backend to start...
timeout /t 15 /nobreak > nul

echo.
echo 🧪 Testing endpoints...

echo Testing health endpoint...
curl -s http://localhost:8081/api/health
echo.

echo Testing auth endpoint...
curl -s http://localhost:8081/auth/test
echo.

echo Testing login with admin credentials...
curl -s -X POST http://localhost:8081/auth/signin -H "Content-Type: application/json" -d "{\"username\":\"admin\",\"password\":\"admin123\"}"
echo.

echo.
echo ========================================
echo  Test Complete
echo ========================================
echo.
echo If you see JSON responses above, the backend is working!
echo If you see connection errors, the backend failed to start.
echo.
pause

REM Kill the background Java process
taskkill /F /IM java.exe 2>nul
