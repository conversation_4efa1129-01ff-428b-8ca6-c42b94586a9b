@echo off
echo ========================================
echo  FIXING LOGIN ISSUE NOW
echo ========================================
echo.

REM Kill any existing Java processes
taskkill /F /IM java.exe 2>nul

cd /d "C:\Users\<USER>\Desktop\Model\agriculture-app\backend"

echo 1. Building backend...
call mvn clean package -DskipTests -q

echo 2. Starting backend...
echo    Backend will run on: http://localhost:8081
echo    Please wait 30 seconds for startup...
echo.

REM Start backend and capture output
start "Agriculture Backend" cmd /k "java -jar target\crop-prediction-backend-0.0.1-SNAPSHOT.jar"

echo 3. Waiting for backend startup...
timeout /t 30 /nobreak > nul

echo 4. Testing backend connection...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/api/health' -UseBasicParsing; Write-Host 'Backend is running!' } catch { Write-Host 'Backend failed to start' }"

echo.
echo 5. Testing login endpoint...
powershell -Command "try { $body = @{username='admin';password='admin123'} | ConvertTo-Json; $response = Invoke-WebRequest -Uri 'http://localhost:8081/auth/signin' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing; Write-Host 'Login endpoint working!' } catch { Write-Host 'Login endpoint failed:' $_.Exception.Message }"

echo.
echo ========================================
echo  BACKEND STATUS CHECK COMPLETE
echo ========================================
echo.
echo If you see "Backend is running!" above, the backend is working.
echo If you see "Login endpoint working!" above, authentication is working.
echo.
echo Now start the frontend:
echo   1. Open a new Command Prompt
echo   2. cd C:\Users\<USER>\Desktop\Model\agriculture-app\frontend
echo   3. npm start
echo   4. Go to http://localhost:3000
echo   5. Login with: admin / admin123
echo.
pause
