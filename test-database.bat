@echo off
echo ========================================
echo  Database Connection Test
echo ========================================
echo.

cd /d "C:\Users\<USER>\Desktop\Model\agriculture-app\backend"

echo 🗄️ Cleaning old database files...
if exist "data\agriculture_db.mv.db" del "data\agriculture_db.mv.db"
if exist "data\agriculture_db.trace.db" del "data\agriculture_db.trace.db"
echo ✅ Database files cleaned!

echo.
echo 🔧 Building backend...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo 🚀 Starting backend for database test...
echo ⏳ This will take about 30 seconds...
echo.

start "Database Test" java -jar target\crop-prediction-backend-0.0.1-SNAPSHOT.jar

echo ⏳ Waiting for backend to initialize database...
timeout /t 20 /nobreak > nul

echo.
echo 🧪 Testing database endpoints...
echo.

echo Testing health endpoint...
curl -s http://localhost:8081/api/health
echo.
echo.

echo Testing H2 console access...
curl -s -I http://localhost:8081/api/h2-console | findstr "200\|302"
echo.

echo Testing authentication endpoint...
curl -s http://localhost:8081/auth/test
echo.
echo.

echo ========================================
echo  Database Test Complete
echo ========================================
echo.
echo 🌐 H2 Console: http://localhost:8081/api/h2-console
echo 📊 Connection URL: jdbc:h2:file:./data/agriculture_db
echo 👤 Username: sa
echo 🔑 Password: (empty)
echo.
echo ⚠️ If you see errors above, the database is not working properly.
echo ✅ If you see responses, the database is working!
echo.
pause
