# Server Configuration
server.port=8081
# server.servlet.context-path=/api  # Commented out - controllers already have /api prefix

# H2 In-Memory Database Configuration (for testing)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 Console (optional - for viewing database)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true

# MySQL Database Configuration (commented out for now)
# spring.datasource.url=******************************************************************************************************************
# spring.datasource.username=root
# spring.datasource.password=password
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration for H2 (in-memory database)
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.open-in-view=false

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# JWT Configuration
app.jwt.secret=bXlTZWNyZXRLZXlGb3JBZ3JpY3VsdHVyZUFwcGxpY2F0aW9uVGhhdElzU2VjdXJlQW5kTG9uZ0Vub3VnaEZvckpXVA==
app.jwt.expiration=86400000

# CORS Configuration
app.cors.allowed-origins=http://localhost:3000

# Logging Configuration
logging.level.com.agriculture.cropprediction=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.orm.deprecation=ERROR
logging.level.org.apache.commons.logging=ERROR
logging.level.org.springframework.web.servlet.handler.HandlerMappingIntrospector=ERROR

# Python ML Model Configuration (disabled for database testing)
app.ml.python-path=python
app.ml.model-path=../ml-models/
app.ml.script-path=../ml-models/Core Files/agricultural_ml_analysis.py
app.ml.enabled=false
