[{"C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\LoginPage.js": "3", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\HomePage.js": "5", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\Navbar.js": "6", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\SignupPage.js": "7", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\AdminPanel.js": "8", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\context\\AuthContext.js": "9", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\Dashboard.js": "10", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\AdminPredictions.js": "11", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\UserManagement.js": "12", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\PredictionResults.js": "13", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\AdminUploads.js": "14", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\AdminDashboard.js": "15", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\DashboardStats.js": "16", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\FileUploadComponent.js": "17", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\UploadHistory.js": "18", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\services\\apiService.js": "19", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\services\\authService.js": "20", "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\IoTDashboard.js": "21"}, {"size": 254, "mtime": 1750924615694, "results": "22", "hashOfConfig": "23"}, {"size": 2598, "mtime": 1757927351510, "results": "24", "hashOfConfig": "23"}, {"size": 5565, "mtime": 1750924944407, "results": "25", "hashOfConfig": "23"}, {"size": 698, "mtime": 1750924691531, "results": "26", "hashOfConfig": "23"}, {"size": 10719, "mtime": 1757935432758, "results": "27", "hashOfConfig": "23"}, {"size": 7375, "mtime": 1757927396130, "results": "28", "hashOfConfig": "23"}, {"size": 10105, "mtime": 1750924976508, "results": "29", "hashOfConfig": "23"}, {"size": 4477, "mtime": 1750925120104, "results": "30", "hashOfConfig": "23"}, {"size": 2616, "mtime": 1757951120578, "results": "31", "hashOfConfig": "23"}, {"size": 5448, "mtime": 1750925008081, "results": "32", "hashOfConfig": "23"}, {"size": 6985, "mtime": 1750925220870, "results": "33", "hashOfConfig": "23"}, {"size": 5824, "mtime": 1750925171484, "results": "34", "hashOfConfig": "23"}, {"size": 7907, "mtime": 1750924913609, "results": "35", "hashOfConfig": "23"}, {"size": 6866, "mtime": 1750925195420, "results": "36", "hashOfConfig": "23"}, {"size": 7103, "mtime": 1750925147980, "results": "37", "hashOfConfig": "23"}, {"size": 9563, "mtime": 1750925063933, "results": "38", "hashOfConfig": "23"}, {"size": 4319, "mtime": 1750925029310, "results": "39", "hashOfConfig": "23"}, {"size": 6408, "mtime": 1750925088032, "results": "40", "hashOfConfig": "23"}, {"size": 2865, "mtime": 1750924682670, "results": "41", "hashOfConfig": "23"}, {"size": 1896, "mtime": 1757950690941, "results": "42", "hashOfConfig": "23"}, {"size": 13814, "mtime": 1757936232767, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "j1z39m", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\ProtectedRoute.js", ["107"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\SignupPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\AdminPanel.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\pages\\Dashboard.js", ["108", "109", "110", "111", "112", "113"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\AdminPredictions.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\UserManagement.js", ["114", "115"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\PredictionResults.js", ["116"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\AdminUploads.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\DashboardStats.js", ["117", "118"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\FileUploadComponent.js", [], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\UploadHistory.js", ["119"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\services\\apiService.js", ["120"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\services\\authService.js", ["121"], [], "C:\\Users\\<USER>\\Desktop\\Model\\agriculture-app\\frontend\\src\\components\\IoTDashboard.js", ["122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132"], [], {"ruleId": "133", "severity": 1, "message": "134", "line": 7, "column": 11, "nodeType": "135", "messageId": "136", "endLine": 7, "endColumn": 15}, {"ruleId": "133", "severity": 1, "message": "137", "line": 14, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 14, "endColumn": 7}, {"ruleId": "133", "severity": 1, "message": "138", "line": 15, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 15, "endColumn": 13}, {"ruleId": "133", "severity": 1, "message": "139", "line": 26, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 26, "endColumn": 9}, {"ruleId": "133", "severity": 1, "message": "140", "line": 27, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 27, "endColumn": 13}, {"ruleId": "133", "severity": 1, "message": "141", "line": 28, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 28, "endColumn": 13}, {"ruleId": "133", "severity": 1, "message": "142", "line": 29, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 29, "endColumn": 14}, {"ruleId": "133", "severity": 1, "message": "138", "line": 13, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 13, "endColumn": 13}, {"ruleId": "133", "severity": 1, "message": "143", "line": 25, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 25, "endColumn": 9}, {"ruleId": "133", "severity": 1, "message": "144", "line": 22, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 22, "endColumn": 14}, {"ruleId": "133", "severity": 1, "message": "145", "line": 14, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 14, "endColumn": 8}, {"ruleId": "133", "severity": 1, "message": "146", "line": 15, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 15, "endColumn": 17}, {"ruleId": "133", "severity": 1, "message": "147", "line": 24, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 24, "endColumn": 9}, {"ruleId": "148", "severity": 1, "message": "149", "line": 110, "column": 1, "nodeType": "150", "endLine": 110, "endColumn": 33}, {"ruleId": "148", "severity": 1, "message": "149", "line": 84, "column": 1, "nodeType": "150", "endLine": 84, "endColumn": 34}, {"ruleId": "133", "severity": 1, "message": "145", "line": 14, "column": 3, "nodeType": "135", "messageId": "136", "endLine": 14, "endColumn": 8}, {"ruleId": "133", "severity": 1, "message": "151", "line": 31, "column": 8, "nodeType": "135", "messageId": "136", "endLine": 31, "endColumn": 17}, {"ruleId": "133", "severity": 1, "message": "152", "line": 33, "column": 10, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 19}, {"ruleId": "133", "severity": 1, "message": "153", "line": 33, "column": 21, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 25}, {"ruleId": "133", "severity": 1, "message": "154", "line": 33, "column": 27, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 32}, {"ruleId": "133", "severity": 1, "message": "155", "line": 33, "column": 34, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 39}, {"ruleId": "133", "severity": 1, "message": "156", "line": 33, "column": 41, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 54}, {"ruleId": "133", "severity": 1, "message": "157", "line": 33, "column": 67, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 82}, {"ruleId": "133", "severity": 1, "message": "158", "line": 33, "column": 84, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 103}, {"ruleId": "133", "severity": 1, "message": "159", "line": 33, "column": 105, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 114}, {"ruleId": "133", "severity": 1, "message": "160", "line": 33, "column": 116, "nodeType": "135", "messageId": "136", "endLine": 33, "endColumn": 120}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'Chip' is defined but never used.", "'IconButton' is defined but never used.", "'Delete' is defined but never used.", "'Visibility' is defined but never used.", "'TrendingUp' is defined but never used.", "'Agriculture' is defined but never used.", "'Person' is defined but never used.", "'CheckCircle' is defined but never used.", "'Paper' is defined but never used.", "'LinearProgress' is defined but never used.", "'GetApp' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'ErrorIcon' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'RechartsTooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'AreaChart' is defined but never used.", "'Area' is defined but never used."]