2025-09-15 16:59:07.202454+01:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "create table iot_sensor_data (id bigint generated by default as identity, created_at timestamp(6), device_status varchar(255), latitude float(53), location varchar(255), longitude float(53), sensor_id varchar(255) not null, sensor_type varchar(255) not null, timestamp timestamp(6) not null, unit varchar(255), updated_at timestamp(6), [*]value float(53) not null, primary key (id))"; expected "identifier"; SQL statement:
create table iot_sensor_data (id bigint generated by default as identity, created_at timestamp(6), device_status varchar(255), latitude float(53), location varchar(255), longitude float(53), sensor_id varchar(255) not null, sensor_type varchar(255) not null, timestamp timestamp(6) not null, unit varchar(255), updated_at timestamp(6), value float(53) not null, primary key (id)) [42001-224]
2025-09-15 16:59:13.241998+01:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLNonTransientException: The object is already closed [90007-224]
2025-09-15 16:59:13.439133+01:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLNonTransientException: The object is already closed [90007-224]
2025-09-15 16:59:14.022138+01:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLNonTransientException: The object is already closed [90007-224]
2025-09-15 16:59:14.604116+01:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLNonTransientException: The object is already closed [90007-224]
2025-09-15 17:03:19.390206+01:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "create table iot_sensor_data (id bigint generated by default as identity, created_at timestamp(6), device_status varchar(255), latitude float(53), location varchar(255), longitude float(53), sensor_id varchar(255) not null, sensor_type varchar(255) not null, timestamp timestamp(6) not null, unit varchar(255), updated_at timestamp(6), [*]value float(53) not null, primary key (id))"; expected "identifier"; SQL statement:
create table iot_sensor_data (id bigint generated by default as identity, created_at timestamp(6), device_status varchar(255), latitude float(53), location varchar(255), longitude float(53), sensor_id varchar(255) not null, sensor_type varchar(255) not null, timestamp timestamp(6) not null, unit varchar(255), updated_at timestamp(6), value float(53) not null, primary key (id)) [42001-224]
2025-09-15 18:42:23.482862+01:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Syntax error in SQL statement "create table iot_sensor_data (id bigint generated by default as identity, created_at timestamp(6), device_status varchar(255), latitude float(53), location varchar(255), longitude float(53), sensor_id varchar(255) not null, sensor_type varchar(255) not null, timestamp timestamp(6) not null, unit varchar(255), updated_at timestamp(6), [*]value float(53) not null, primary key (id))"; expected "identifier"; SQL statement:
create table iot_sensor_data (id bigint generated by default as identity, created_at timestamp(6), device_status varchar(255), latitude float(53), location varchar(255), longitude float(53), sensor_id varchar(255) not null, sensor_type varchar(255) not null, timestamp timestamp(6) not null, unit varchar(255), updated_at timestamp(6), value float(53) not null, primary key (id)) [42001-224]
