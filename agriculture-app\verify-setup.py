#!/usr/bin/env python3
"""
Verification script to ensure the agriculture application is properly set up after cleanup.
"""

import os
import sys

def check_file_exists(filepath, description):
    """Check if a file exists and print status."""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False

def check_directory_structure():
    """Check the main directory structure."""
    print("🔍 Checking Directory Structure")
    print("=" * 50)
    
    required_dirs = [
        ("Backend source", "backend/src/main/java/com/agriculture/cropprediction"),
        ("Frontend source", "frontend/src"),
        ("ML Models", "ml-models"),
        ("Test data", "test-data"),
        ("Documentation", "docs")
    ]
    
    all_good = True
    for desc, path in required_dirs:
        if not check_file_exists(path, desc):
            all_good = False
    
    return all_good

def check_key_files():
    """Check key application files."""
    print("\n🔍 Checking Key Application Files")
    print("=" * 50)
    
    key_files = [
        ("Backend POM", "backend/pom.xml"),
        ("Application Properties", "backend/src/main/resources/application.properties"),
        ("Main Application Class", "backend/src/main/java/com/agriculture/cropprediction/CropPredictionApplication.java"),
        ("Auth Controller", "backend/src/main/java/com/agriculture/cropprediction/controller/AuthController.java"),
        ("Frontend Package", "frontend/package.json"),
        ("Frontend App", "frontend/src/App.js"),
        ("ML Analysis Script", "ml-models/Core Files/agricultural_ml_analysis.py"),
        ("Test Data Sample", "test-data/farm_data_1.csv")
    ]
    
    all_good = True
    for desc, path in key_files:
        if not check_file_exists(path, desc):
            all_good = False
    
    return all_good

def check_no_duplicates():
    """Check that duplicate files have been removed."""
    print("\n🔍 Checking for Duplicate Files (should be removed)")
    print("=" * 50)
    
    # Check root directory for files that should not be there
    root_files_to_check = [
        "crop_label_encoder.pkl",
        "crop_model.pkl", 
        "test_parsing.py",
        "quick_test.py",
        "start_and_test.py"
    ]
    
    duplicates_found = False
    for filename in root_files_to_check:
        root_path = f"../{filename}"
        if os.path.exists(root_path):
            print(f"❌ Duplicate found in root: {filename}")
            duplicates_found = True
        else:
            print(f"✅ No duplicate in root: {filename}")
    
    # Check for duplicate directories in root
    duplicate_dirs = ["Core Files", "Data Files", "Generated Outputes", "__pycache__"]
    for dirname in duplicate_dirs:
        root_path = f"../{dirname}"
        if os.path.exists(root_path):
            print(f"❌ Duplicate directory found in root: {dirname}")
            duplicates_found = True
        else:
            print(f"✅ No duplicate directory in root: {dirname}")
    
    return not duplicates_found

def check_configuration():
    """Check application configuration."""
    print("\n🔍 Checking Application Configuration")
    print("=" * 50)
    
    config_file = "backend/src/main/resources/application.properties"
    if not os.path.exists(config_file):
        print(f"❌ Configuration file not found: {config_file}")
        return False
    
    try:
        with open(config_file, 'r') as f:
            content = f.read()
            
        # Check for key configurations
        checks = [
            ("Server port", "server.port=8081"),
            ("JWT secret", "app.jwt.secret="),
            ("CORS origins", "app.cors.allowed-origins="),
            ("H2 database", "spring.datasource.url=jdbc:h2:"),
            ("ML script path", "app.ml.script-path=")
        ]
        
        all_good = True
        for desc, config_key in checks:
            if config_key in content:
                print(f"✅ {desc} configured")
            else:
                print(f"❌ {desc} missing: {config_key}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False

def main():
    """Main verification function."""
    print("🧪 Agriculture Application Setup Verification")
    print("=" * 60)
    
    # Change to agriculture-app directory
    if os.path.exists("agriculture-app"):
        os.chdir("agriculture-app")
    elif not os.path.exists("backend"):
        print("❌ Not in agriculture-app directory and can't find it!")
        return False
    
    # Run all checks
    checks = [
        ("Directory Structure", check_directory_structure),
        ("Key Files", check_key_files),
        ("No Duplicates", check_no_duplicates),
        ("Configuration", check_configuration)
    ]
    
    all_passed = True
    results = {}
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results[check_name] = False
            all_passed = False
    
    # Summary
    print("\n📊 Verification Summary")
    print("=" * 50)
    for check_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{check_name}: {status}")
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All checks passed! Application is properly set up.")
        print("\nNext steps:")
        print("1. Start backend: cd backend && mvn spring-boot:run")
        print("2. Start frontend: cd frontend && npm start")
        print("3. Open http://localhost:3000")
        print("4. Login with admin/admin123")
    else:
        print("❌ Some checks failed. Please review the issues above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
