@echo off
echo ========================================
echo  Smart Agriculture IoT System
echo  Quick Start Script
echo ========================================
echo.

cd /d "C:\Users\<USER>\Desktop\Model"

echo �️ Cleaning database files...
if exist "agriculture-app\backend\data\agriculture_db.mv.db" del "agriculture-app\backend\data\agriculture_db.mv.db"
if exist "agriculture-app\backend\data\agriculture_db.trace.db" del "agriculture-app\backend\data\agriculture_db.trace.db"
echo ✅ Database files cleaned!

echo �🔧 Building backend...
cd agriculture-app\backend
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ Backend build failed!
    pause
    exit /b 1
)

echo ✅ Backend built successfully!
echo.
echo 🚀 Starting backend server...
echo ⚠️ If backend fails to start, run debug-backend.bat to see errors
start "Agriculture Backend" java -jar target\crop-prediction-backend-0.0.1-SNAPSHOT.jar

echo ⏳ Waiting for backend to start...
timeout /t 20 /nobreak > nul

echo 🧪 Testing backend connection...
curl -s http://localhost:8081/api/health > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Backend not responding!
    echo 🔧 Try running debug-backend.bat to see error details
    echo.
) else (
    echo ✅ Backend is running!
)

echo.
echo 🌐 Starting frontend...
cd ..\frontend
start "Agriculture Frontend" cmd /c "npm start"

echo.
echo ========================================
echo  🎉 System Starting!
echo ========================================
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:8081/api
echo 🗄️ Database: http://localhost:8081/api/h2-console
echo 🌡️ IoT Dashboard: http://localhost:3000/iot
echo.
echo 🔐 Default Login Credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo    OR
echo.
echo    Username: testuser  
echo    Password: test123
echo.
echo ⚠️ Note: Wait 30 seconds for all services to fully start
echo.
pause
