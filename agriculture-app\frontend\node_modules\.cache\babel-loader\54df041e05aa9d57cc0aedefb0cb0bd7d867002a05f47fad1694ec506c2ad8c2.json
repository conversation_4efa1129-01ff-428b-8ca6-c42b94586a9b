{"ast": null, "code": "import axios from 'axios';\nconst API_URL = '/auth';\nclass AuthService {\n  constructor() {\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // Request interceptor to add auth token\n    axios.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor to handle auth errors\n    axios.interceptors.response.use(response => response, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        this.removeAuthToken();\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n      }\n      return Promise.reject(error);\n    });\n  }\n  async login(username, password) {\n    const response = await axios.post(`${API_URL}/signin`, {\n      username,\n      password\n    });\n    return response.data;\n  }\n  async signup(userData) {\n    const response = await axios.post(`${API_URL}/signup`, userData);\n    return response.data;\n  }\n  setAuthToken(token) {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n  }\n  removeAuthToken() {\n    delete axios.defaults.headers.common['Authorization'];\n  }\n  getCurrentUser() {\n    const userData = localStorage.getItem('user');\n    return userData ? JSON.parse(userData) : null;\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    return !!(token && user);\n  }\n  isAdmin() {\n    var _user$roles;\n    const user = this.getCurrentUser();\n    return (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('ROLE_ADMIN')) || false;\n  }\n}\nexport default new AuthService();", "map": {"version": 3, "names": ["axios", "API_URL", "AuthService", "constructor", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeAuthToken", "removeItem", "window", "location", "href", "login", "username", "password", "post", "data", "signup", "userData", "setAuthToken", "defaults", "common", "getCurrentUser", "JSON", "parse", "getToken", "isAuthenticated", "user", "isAdmin", "_user$roles", "roles", "includes"], "sources": ["C:/Users/<USER>/Desktop/Model/agriculture-app/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = '/auth';\n\nclass AuthService {\n  constructor() {\n    this.setupInterceptors();\n  }\n\n  setupInterceptors() {\n    // Request interceptor to add auth token\n    axios.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor to handle auth errors\n    axios.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          this.removeAuthToken();\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          window.location.href = '/login';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  async login(username, password) {\n    const response = await axios.post(`${API_URL}/signin`, {\n      username,\n      password\n    });\n    return response.data;\n  }\n\n  async signup(userData) {\n    const response = await axios.post(`${API_URL}/signup`, userData);\n    return response.data;\n  }\n\n  setAuthToken(token) {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n  }\n\n  removeAuthToken() {\n    delete axios.defaults.headers.common['Authorization'];\n  }\n\n  getCurrentUser() {\n    const userData = localStorage.getItem('user');\n    return userData ? JSON.parse(userData) : null;\n  }\n\n  getToken() {\n    return localStorage.getItem('token');\n  }\n\n  isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    return !!(token && user);\n  }\n\n  isAdmin() {\n    const user = this.getCurrentUser();\n    return user?.roles?.includes('ROLE_ADMIN') || false;\n  }\n}\n\nexport default new AuthService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,OAAO;AAEvB,MAAMC,WAAW,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEAA,iBAAiBA,CAAA,EAAG;IAClB;IACAJ,KAAK,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC3BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAM,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACAd,KAAK,CAACK,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC5BU,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC,IAAI,CAACC,eAAe,CAAC,CAAC;QACtBV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;QAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;MACA,OAAOT,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEA,MAAMW,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC9B,MAAMV,QAAQ,GAAG,MAAMjB,KAAK,CAAC4B,IAAI,CAAC,GAAG3B,OAAO,SAAS,EAAE;MACrDyB,QAAQ;MACRC;IACF,CAAC,CAAC;IACF,OAAOV,QAAQ,CAACY,IAAI;EACtB;EAEA,MAAMC,MAAMA,CAACC,QAAQ,EAAE;IACrB,MAAMd,QAAQ,GAAG,MAAMjB,KAAK,CAAC4B,IAAI,CAAC,GAAG3B,OAAO,SAAS,EAAE8B,QAAQ,CAAC;IAChE,OAAOd,QAAQ,CAACY,IAAI;EACtB;EAEAG,YAAYA,CAACvB,KAAK,EAAE;IAClB,IAAIA,KAAK,EAAE;MACTT,KAAK,CAACiC,QAAQ,CAACrB,OAAO,CAACsB,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUzB,KAAK,EAAE;IACpE;EACF;EAEAW,eAAeA,CAAA,EAAG;IAChB,OAAOpB,KAAK,CAACiC,QAAQ,CAACrB,OAAO,CAACsB,MAAM,CAAC,eAAe,CAAC;EACvD;EAEAC,cAAcA,CAAA,EAAG;IACf,MAAMJ,QAAQ,GAAGrB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC7C,OAAOoB,QAAQ,GAAGK,IAAI,CAACC,KAAK,CAACN,QAAQ,CAAC,GAAG,IAAI;EAC/C;EAEAO,QAAQA,CAAA,EAAG;IACT,OAAO5B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACtC;EAEA4B,eAAeA,CAAA,EAAG;IAChB,MAAM9B,KAAK,GAAG,IAAI,CAAC6B,QAAQ,CAAC,CAAC;IAC7B,MAAME,IAAI,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC;IAClC,OAAO,CAAC,EAAE1B,KAAK,IAAI+B,IAAI,CAAC;EAC1B;EAEAC,OAAOA,CAAA,EAAG;IAAA,IAAAC,WAAA;IACR,MAAMF,IAAI,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC;IAClC,OAAO,CAAAK,IAAI,aAAJA,IAAI,wBAAAE,WAAA,GAAJF,IAAI,CAAEG,KAAK,cAAAD,WAAA,uBAAXA,WAAA,CAAaE,QAAQ,CAAC,YAAY,CAAC,KAAI,KAAK;EACrD;AACF;AAEA,eAAe,IAAI1C,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}