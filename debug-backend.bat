@echo off
echo ========================================
echo  Backend Debug - Database Issues
echo ========================================
echo.

cd /d "C:\Users\<USER>\Desktop\Model\agriculture-app\backend"

echo 🗄️ Cleaning database files...
if exist "data\agriculture_db.mv.db" del "data\agriculture_db.mv.db"
if exist "data\agriculture_db.trace.db" del "data\agriculture_db.trace.db"
echo ✅ Database files cleaned!

echo.
echo 🔧 Rebuilding backend...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo 🚀 Starting backend with debug output...
echo ⚠️ Watch for any error messages below:
echo.

java -jar target\crop-prediction-backend-0.0.1-SNAPSHOT.jar

echo.
echo ========================================
echo  Backend stopped - check errors above
echo ========================================
pause
