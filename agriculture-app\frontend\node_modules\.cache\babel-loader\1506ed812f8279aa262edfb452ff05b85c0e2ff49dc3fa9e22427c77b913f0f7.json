{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Model\\\\agriculture-app\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport authService from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const initializeAuth = () => {\n      const token = localStorage.getItem('token');\n      const userData = localStorage.getItem('user');\n      if (token && userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          authService.setAuthToken(token);\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n      setLoading(false);\n    };\n    initializeAuth();\n  }, []);\n  const login = async (username, password) => {\n    try {\n      const response = await authService.login(username, password);\n      console.log('Login response:', response); // Debug log\n\n      // Backend returns 'token', not 'accessToken'\n      const {\n        token,\n        ...userData\n      } = response;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(userData));\n      authService.setAuthToken(token);\n      setUser(userData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login error:', error);\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const signup = async userData => {\n    try {\n      await authService.signup(userData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Signup error:', error);\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Signup failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    authService.removeAuthToken();\n    setUser(null);\n  };\n  const isAdmin = () => {\n    var _user$roles;\n    return (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('ROLE_ADMIN')) || false;\n  };\n  const isAuthenticated = () => {\n    return !!user;\n  };\n  const value = {\n    user,\n    login,\n    signup,\n    logout,\n    isAdmin,\n    isAuthenticated,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "initializeAuth", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "setAuthToken", "error", "console", "removeItem", "login", "username", "password", "response", "log", "setItem", "stringify", "success", "_error$response", "_error$response$data", "message", "data", "signup", "_error$response2", "_error$response2$data", "logout", "removeAuthToken", "isAdmin", "_user$roles", "roles", "includes", "isAuthenticated", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Model/agriculture-app/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport authService from '../services/authService';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initializeAuth = () => {\n      const token = localStorage.getItem('token');\n      const userData = localStorage.getItem('user');\n      \n      if (token && userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          authService.setAuthToken(token);\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n      setLoading(false);\n    };\n\n    initializeAuth();\n  }, []);\n\n  const login = async (username, password) => {\n    try {\n      const response = await authService.login(username, password);\n      console.log('Login response:', response); // Debug log\n\n      // Backend returns 'token', not 'accessToken'\n      const { token, ...userData } = response;\n\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(userData));\n      authService.setAuthToken(token);\n      setUser(userData);\n\n      return { success: true };\n    } catch (error) {\n      console.error('Login error:', error);\n      return {\n        success: false,\n        message: error.response?.data?.message || 'Login failed'\n      };\n    }\n  };\n\n  const signup = async (userData) => {\n    try {\n      await authService.signup(userData);\n      return { success: true };\n    } catch (error) {\n      console.error('Signup error:', error);\n      return { \n        success: false, \n        message: error.response?.data?.message || 'Signup failed' \n      };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    authService.removeAuthToken();\n    setUser(null);\n  };\n\n  const isAdmin = () => {\n    return user?.roles?.includes('ROLE_ADMIN') || false;\n  };\n\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  const value = {\n    user,\n    login,\n    signup,\n    logout,\n    isAdmin,\n    isAuthenticated,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAE5CE,SAAS,CAAC,MAAM;IACd,MAAMgB,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE7C,IAAIF,KAAK,IAAIG,QAAQ,EAAE;QACrB,IAAI;UACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;UACvCP,OAAO,CAACQ,UAAU,CAAC;UACnBpB,WAAW,CAACuB,YAAY,CAACP,KAAK,CAAC;QACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChDP,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;UAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;QACjC;MACF;MACAZ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,WAAW,CAAC2B,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC;MAC5DJ,OAAO,CAACM,GAAG,CAAC,iBAAiB,EAAED,QAAQ,CAAC,CAAC,CAAC;;MAE1C;MACA,MAAM;QAAEd,KAAK;QAAE,GAAGG;MAAS,CAAC,GAAGW,QAAQ;MAEvCb,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEhB,KAAK,CAAC;MACpCC,YAAY,CAACe,OAAO,CAAC,MAAM,EAAEX,IAAI,CAACY,SAAS,CAACd,QAAQ,CAAC,CAAC;MACtDnB,WAAW,CAACuB,YAAY,CAACP,KAAK,CAAC;MAC/BJ,OAAO,CAACO,QAAQ,CAAC;MAEjB,OAAO;QAAEe,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACdX,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO;QACLU,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAF,eAAA,GAAAX,KAAK,CAACM,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAME,MAAM,GAAG,MAAOpB,QAAQ,IAAK;IACjC,IAAI;MACF,MAAMnB,WAAW,CAACuC,MAAM,CAACpB,QAAQ,CAAC;MAClC,OAAO;QAAEe,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACdhB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QACLU,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAG,gBAAA,GAAAhB,KAAK,CAACM,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBF,IAAI,cAAAG,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnBzB,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/B1B,WAAW,CAAC2C,eAAe,CAAC,CAAC;IAC7B/B,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMgC,OAAO,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA;IACpB,OAAO,CAAAlC,IAAI,aAAJA,IAAI,wBAAAkC,WAAA,GAAJlC,IAAI,CAAEmC,KAAK,cAAAD,WAAA,uBAAXA,WAAA,CAAaE,QAAQ,CAAC,YAAY,CAAC,KAAI,KAAK;EACrD,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAACrC,IAAI;EACf,CAAC;EAED,MAAMsC,KAAK,GAAG;IACZtC,IAAI;IACJgB,KAAK;IACLY,MAAM;IACNG,MAAM;IACNE,OAAO;IACPI,eAAe;IACfnC;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAAC+C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAxC,QAAA,EAChCA;EAAQ;IAAA0C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC5C,GAAA,CA5FWF,YAAY;AAAA+C,EAAA,GAAZ/C,YAAY;AAAA,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}