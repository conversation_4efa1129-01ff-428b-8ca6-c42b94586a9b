@echo off
echo ========================================
echo  Minimal Database Test
echo ========================================
echo.

cd /d "C:\Users\<USER>\Desktop\Model\agriculture-app\backend"

echo 🗄️ Cleaning database...
if exist "data\agriculture_db.mv.db" del "data\agriculture_db.mv.db"
if exist "data\agriculture_db.trace.db" del "data\agriculture_db.trace.db"

echo 🔧 Testing Java and JAR...
java -version
echo.

if not exist "target\crop-prediction-backend-0.0.1-SNAPSHOT.jar" (
    echo ❌ JAR file not found - building...
    call mvn clean package -DskipTests
)

echo.
echo 🚀 Starting backend with verbose output...
echo ⚠️ Press Ctrl+C to stop if it hangs
echo.

java -Dspring.profiles.active=dev -Dlogging.level.root=INFO -jar target\crop-prediction-backend-0.0.1-SNAPSHOT.jar

pause
