@echo off
cd /d "C:\Users\<USER>\Desktop\Model\agriculture-app\backend"

echo Testing Java version...
java -version

echo.
echo Testing if JAR file exists...
if exist "target\crop-prediction-backend-0.0.1-SNAPSHOT.jar" (
    echo ✅ JAR file found
) else (
    echo ❌ JAR file not found - need to build first
    exit /b 1
)

echo.
echo Testing JAR file integrity...
java -jar target\crop-prediction-backend-0.0.1-SNAPSHOT.jar --help 2>&1 | findstr "Usage\|Error\|Exception"

echo.
echo Creating data directory...
if not exist "data" mkdir data

echo.
echo Testing database connection with simple H2 test...
java -cp target\crop-prediction-backend-0.0.1-SNAPSHOT.jar org.h2.tools.Console -web -webPort 9999 -webAllowOthers

pause
